import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import request from 'supertest';
import express, { Request, Response } from 'express';

// Mock authentication middleware
vi.mock('../server/middleware/auth', () => ({
  requireAuth: (_req: Request, _res: Response, next: () => void) => {
    (_req as any).accessToken = 'test-token';
    (_req as any).user = { id: 'test-user-id' };
    next();
  },
  getAccessToken: (_req: Request) => 'test-token',
}));

function createSupabaseMock() {
  return {
    from: vi.fn(() => ({
      select: vi.fn().mockReturnThis(),
      order: vi.fn().mockReturnThis(),
      insert: vi.fn().mockReturnThis(),
      update: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      single: vi.fn().mockReturnThis(),
      delete: vi.fn().mockReturnThis(),
    })),
  };
}

describe('Document Versions API', () => {
  let app: express.Express;
  let supabaseMock: ReturnType<typeof createSupabaseMock>;
  const documentId = '550e8400-e29b-41d4-a716-446655440002';
  const versionId = '550e8400-e29b-41d4-a716-446655440001';

  beforeEach(async () => {
    // Set up test environment
    process.env.TEST_BYPASS_AUTH = '1';
    
    app = express();
    app.use(express.json());
    supabaseMock = createSupabaseMock();
    
    vi.doMock('../server/supabaseClient', () => ({
      getUserClient: () => supabaseMock,
      supabaseAdmin: supabaseMock,
    }));
    
    // Import routes after mocking
    const { default: documentVersionsRouter } = await import('../server/routes/documentVersions');
    const { default: documentsRouter } = await import('../server/routes/documents');
    
    app.use('/api/document-versions', documentVersionsRouter);
    app.use('/api/documents', documentsRouter);
  });

  afterEach(() => {
    vi.clearAllMocks();
    vi.resetModules();
  });

  describe('Version Creation on Document Update', () => {
    it('should create a version when document content is updated', async () => {
      const mockDocument = {
        id: documentId,
        content: 'Initial content'
      };
      
      let callCount = 0;
      supabaseMock.from.mockImplementation((): any => {
        callCount++;
        if (callCount === 1) {
          // First call: fetch current document
          return {
            select: vi.fn().mockReturnThis(),
            eq: vi.fn().mockReturnThis(),
            single: vi.fn().mockResolvedValue({
              data: mockDocument,
              error: null
            })
          };
        } else if (callCount === 2) {
          // Second call: insert version
          return {
            insert: vi.fn().mockResolvedValue({
              data: null,
              error: null
            })
          };
        } else {
          // Third call: update document
          return {
            update: vi.fn().mockReturnThis(),
            eq: vi.fn().mockReturnThis(),
            select: vi.fn().mockReturnThis(),
            single: vi.fn().mockResolvedValue({
              data: { id: documentId, content: 'Updated content' },
              error: null
            })
          };
        }
      });
      
      // Update document content
      const updateResponse = await request(app)
        .put(`/api/documents/${documentId}`)
        .send({
          content: 'Updated content'
        });
      
      expect(updateResponse.status).toBe(200);
      expect(supabaseMock.from).toHaveBeenCalledWith('documents');
      expect(supabaseMock.from).toHaveBeenCalledWith('document_versions');
      expect(supabaseMock.from).toHaveBeenCalledTimes(3);
    });

    it('should not create a version when only non-content fields are updated', async () => {
      let callCount = 0;
      const insertMock = vi.fn();
      (supabaseMock.from as any).mockImplementation((table: string): any => {
        callCount++;
        if (table === 'documents' && callCount === 1) {
          return {
            select: vi.fn().mockReturnThis(),
            eq: vi.fn().mockReturnThis(),
            single: vi.fn().mockResolvedValue({
              data: { id: documentId, name: 'Original Name', content: 'Initial content' },
              error: null
            })
          };
        }
        if (table === 'documents' && callCount === 2) {
          return {
            update: vi.fn().mockReturnThis(),
            eq: vi.fn().mockReturnThis(),
            select: vi.fn().mockReturnThis(),
            single: vi.fn().mockResolvedValue({
              data: { id: documentId, name: 'Updated Name', content: 'Initial content' },
              error: null
            })
          };
        }
        if (table === 'document_versions') {
          return { insert: insertMock };
        }
      });

      const updateResponse = await request(app)
        .put(`/api/documents/${documentId}`)
        .send({
          name: 'Updated Name'
        });

      expect(updateResponse.status).toBe(200);
      expect(insertMock).not.toHaveBeenCalled();
    });
  });

  describe('Version Management', () => {
    it('should retrieve all versions for a document', async () => {
      const mockVersions = [
        { id: 'v1', document_id: documentId, content: 'Initial content', saved_at: '2024-01-01T00:00:00Z' },
        { id: 'v2', document_id: documentId, content: 'Version 1 content', saved_at: '2024-01-01T01:00:00Z' }
      ];
      
      supabaseMock.from.mockReturnValue({
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        order: vi.fn().mockResolvedValue({
          data: mockVersions,
          error: null
        }),
        insert: undefined,
        update: undefined,
        single: undefined,
        delete: undefined
      });
      
      const _from = supabaseMock.from();
      
      const response = await request(app)
        .get(`/api/document-versions/${documentId}/versions`);
      
      expect(response.status).toBe(200);
      expect(response.body.versions).toHaveLength(2);
      expect(response.body.versions[0]).toEqual({
        versionId: 'v1',
        content: 'Initial content',
        savedAt: '2024-01-01T00:00:00Z'
      });
    });

    it('should create a manual version', async () => {
      const testDocumentId = '550e8400-e29b-41d4-a716-446655440000';
      const mockVersion = {
        id: versionId,
        document_id: testDocumentId,
        content: 'Manual version content',
        saved_at: '2024-01-01T00:00:00Z'
      };
      
      // Mock the entire chain for manual version creation
      supabaseMock.from.mockReturnValue({
        insert: vi.fn().mockReturnValue({
          select: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({
              data: mockVersion,
              error: null
            })
          })
        }),
        select: undefined,
        order: undefined,
        update: undefined,
        eq: undefined,
        single: undefined,
        delete: undefined
      });
      
      const from = supabaseMock.from();
      
      const response = await request(app)
        .post('/api/document-versions/manual')
        .send({
          documentId: testDocumentId,
          content: 'Manual version content'
        });
      
      expect(response.status).toBe(201);
      expect(response.body.version).toBeDefined();
      expect(from.insert).toHaveBeenCalledWith({
        document_id: testDocumentId,
        content: 'Manual version content',
        version_type: 'manual'
      });
    });

    it('should delete a version', async () => {
      const mockChain = {
        select: vi.fn().mockReturnThis(),
        eq: vi.fn().mockReturnThis(),
        single: vi.fn().mockResolvedValue({
          data: { id: versionId },
          error: null
        }),
        delete: vi.fn().mockReturnThis()
      };
      
      // Set up eq to return this for chaining, then resolve for final call
      mockChain.eq.mockReturnValueOnce(mockChain); // First eq() call returns this for chaining
      mockChain.eq.mockResolvedValueOnce({ // Second eq() call resolves
        error: null
      });
      
      supabaseMock.from.mockReturnValue({
        select: mockChain.select,
        order: vi.fn(),
        insert: vi.fn(),
        update: vi.fn(),
        eq: mockChain.eq,
        single: mockChain.single,
        delete: mockChain.delete
      });
      
      const from = supabaseMock.from();
      
      const response = await request(app)
        .delete(`/api/document-versions/versions/${versionId}`);
      
      expect(response.status).toBe(204);
      expect(from.delete).toHaveBeenCalled();
      expect(from.eq).toHaveBeenCalledWith('id', versionId);
    });

    it('should restore a version', async () => {
      const mockVersion = {
        document_id: documentId,
        content: 'Version content to restore'
      };
      
      const mockDocument = {
        id: documentId,
        content: 'Current content'
      };
      
      // Mock the entire chain for version fetch
      const versionSelectMock = {
        eq: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue({
            data: mockVersion,
            error: null
          })
        })
      };
      
      // Mock the entire chain for document fetch
      const documentSelectMock = {
        eq: vi.fn().mockReturnValue({
          single: vi.fn().mockResolvedValue({
            data: mockDocument,
            error: null
          })
        })
      };
      
      // Mock the insert for backup version
      const insertMock = {
        insert: vi.fn().mockResolvedValue({ error: null })
      };
      
      // Mock the update for document
      const updateMock = {
        update: vi.fn().mockReturnValue({
          eq: vi.fn().mockResolvedValue({ error: null })
        })
      };
      
      // Set up the from mock to return different chains based on call order
      let callCount = 0;
      supabaseMock.from.mockImplementation((): any => {
        callCount++;
        if (callCount === 1) {
          return { select: vi.fn().mockReturnValue(versionSelectMock) };
        } else if (callCount === 2) {
          return { select: vi.fn().mockReturnValue(documentSelectMock) };
        } else if (callCount === 3) {
          return insertMock;
        } else {
          return updateMock;
        }
      });
      
      const restoreResponse = await request(app)
        .put(`/api/document-versions/versions/${versionId}/restore`);
      
      expect(restoreResponse.status).toBe(200);
      expect(restoreResponse.body.success).toBe(true);
      expect(restoreResponse.body.document.content).toBe('Version content to restore');
    });
  });

  describe('Error Handling', () => {
    it('should handle non-existent document when creating version', async () => {
      // Mock the entire chain for this specific test
      supabaseMock.from.mockReturnValue({
        insert: vi.fn().mockReturnValue({
          select: vi.fn().mockReturnValue({
            single: vi.fn().mockResolvedValue({
              data: null,
              error: { message: 'Document not found', code: '23503' }
            })
          })
        }),
        select: undefined,
        order: undefined,
        update: undefined,
        eq: undefined,
        single: undefined,
        delete: undefined
      });
      
      const response = await request(app)
        .post('/api/document-versions/versions')
        .send({ documentId: '00000000-0000-0000-0000-000000000000', content: 'Test content' });
      
      expect(response.status).toBe(500);
      expect(response.body.error).toBe('Document not found');
    });

    it('should handle non-existent version when deleting', async () => {
      const from = supabaseMock.from();
      from.select.mockReturnThis();
      from.eq.mockReturnThis();
      from.single.mockResolvedValue({
        data: null,
        error: { message: 'Document version not found', code: 'PGRST116' }
      });
      
      const response = await request(app)
        .delete('/api/document-versions/versions/non-existent-version');
      
      expect(response.status).toBe(404);
      expect(response.body.error).toBe('Document version not found');
    });

    it('should handle non-existent version when restoring', async () => {
      const from = supabaseMock.from();
      from.select.mockReturnThis();
      from.eq.mockReturnThis();
      from.single.mockResolvedValue({
        data: null,
        error: { message: 'Document version not found', code: 'PGRST116' }
      });
      
      const response = await request(app)
        .put('/api/document-versions/versions/non-existent-version/restore');
      
      expect(response.status).toBe(404);
      expect(response.body.error).toBe('Document version not found');
    });

    it('should handle database errors gracefully', async () => {
      // Mock the entire chain to return a database error
      supabaseMock.from.mockReturnValue({
        select: vi.fn().mockReturnValue({
          eq: vi.fn().mockReturnValue({
            order: vi.fn().mockResolvedValue({
              data: null,
              error: { message: 'Database connection error', code: 'PGRST301' }
            })
          })
        }),
        order: undefined,
        insert: undefined,
        update: undefined,
        eq: undefined,
        single: undefined,
        delete: undefined
      });
      
      const response = await request(app)
        .get(`/api/document-versions/${documentId}/versions`);
      
      expect(response.status).toBe(500);
      expect(response.body.error).toBe('Database connection error');
    });

    it('should return 404 for non-existent document versions', async () => {
      const fakeDocumentId = '00000000-0000-0000-0000-000000000000';
      
      const from = supabaseMock.from();
      from.select.mockReturnThis();
      from.eq.mockReturnThis();
      from.order.mockResolvedValue({
        data: [],
        error: null
      });
      
      const response = await request(app)
        .get(`/api/document-versions/${fakeDocumentId}/versions`);
      
      expect(response.status).toBe(200);
      expect(response.body.versions).toHaveLength(0);
    });
  });
});