import { describe, it, expect, beforeAll, vi } from 'vitest';
import request from 'supertest';
import { app } from '../server/app';

let createdPath: string | null = null;
let signedPath: string | null = null;

vi.mock('../server/supabaseClient', () => {
  const supabaseAdmin = {
    storage: {
      from: (_bucket: string) => ({
        createSignedUploadUrl: async (path: string) => {
          createdPath = path;
          return { data: { token: 'tok' }, error: null };
        },
        createSignedUrl: async (path: string) => {
          signedPath = path;
          return { data: { signedUrl: `https://signed/${encodeURIComponent(path)}` }, error: null };
        },
        getPublicUrl: (path: string) => ({ data: { publicUrl: `https://public/${encodeURIComponent(path)}` } }),
        remove: async (_paths: string[]) => ({ data: null, error: null }),
      }),
    },
  };

  const getUserClient = (_token: string) => ({
    auth: { getUser: async () => ({ data: { user: { id: 'test-user' } }, error: null }) },
    from: (table: string) => ({
      select: (_cols: string) => ({
        eq: (_col: string, _val: unknown) => ({
          single: async () => {
            if (table === 'client_assets') {
              // return asset row with storage URL
              return { data: { id: 'asset-1', url: 'storage://contractgini/test-user/cli-1/file.pdf' }, error: null };
            }
            return { data: null, error: null };
          },
        }),
      }),
    }),
  });

  return { supabaseAdmin, getUserClient };
});

beforeAll(() => {
  process.env.TEST_BYPASS_AUTH = '1';
});

describe('Client assets routes', () => {
  it('sanitizes filename and prefixes path with userId/clientId', async () => {
    createdPath = null;
    const res = await request(app)
      .post('/api/clients/cli-1/assets/upload-url')
      .send({ fileName: '../evil..//name.pdf' })
      .set('Content-Type', 'application/json');
    expect(res.status).toBe(200);
    expect(res.body).toHaveProperty('token');
    expect(createdPath).toBeTruthy();
    if (createdPath) {
      expect(createdPath).toMatch(/^test-user\/cli-1\//);
      expect(createdPath).toMatch(/name\.pdf$/);
    }
  });

  it('rejects disallowed extension', async () => {
    const res = await request(app)
      .post('/api/clients/cli-1/assets/upload-url')
      .send({ fileName: 'malware.exe' })
      .set('Content-Type', 'application/json');
    expect(res.status).toBe(400);
  });

  it('signs asset URL only when owned by user', async () => {
    signedPath = null;
    const res = await request(app)
      .get('/api/clients/cli-1/assets/asset-1/signed-url?ttl=60');
    expect([200,500]).toContain(res.status);
    if (res.status === 200) {
      expect(res.body).toHaveProperty('url');
      expect(signedPath).toBe('test-user/cli-1/file.pdf');
    }
  });
});

