import { useEffect, useState } from 'react';
import AuthPage from './components/AuthPage';
import CookieConsent from './components/CookieConsent';
import DashboardLayout from './components/DashboardLayout';
import Header from './components/Header';
import LandingPage from './components/LandingPage';
import PrivacyPage from './components/PrivacyPage';
import ResetPasswordPage from './components/ResetPasswordPage';
import SignaturePage from './components/SignaturePage';
import SplashScreen from './components/SplashScreen';
import TermsPage from './components/TermsPage';
import VerificationPage from './components/VerificationPage';
import AdminLayout from './components/admin/AdminLayout';
import { INITIAL_PRIVACY_CONTENT, INITIAL_TERMS_CONTENT } from './constants';
import { apiFetch, setAccessTokenGetter } from './lib/api';
import { fetchPublicPricingPlans, fetchTestimonials } from './lib/content';
import { supabase } from './lib/supabaseClient';
import { Client, Collaborator, DashboardView, Document as DocType, DocumentStatus, DocumentsPaginationState, Flow, FlowRun, NotificationPreferences, Permission, PricingPlan, Team, Template, Testimonial, Theme, User, UserConnection, WorkflowTemplate } from './types';

// Guards to prevent duplicate initialization in React 18 StrictMode (dev)
let __LEXIGEN_INIT_STATIC_BOOT_DONE__ = false;
let __LEXIGEN_INIT_AUTH_BOOT_DONE__ = false;
// Skip the first SIGNED_IN event if we already handled an initial session
let __LEXIGEN_SKIP_NEXT_SIGNED_IN__ = false;

type View = 'home' | 'auth' | 'verification' | 'resetPassword' | 'dashboard' | 'signature' | 'terms' | 'privacy' | 'admin';

const DOCUMENTS_PAGE_SIZE = 20;
const INITIAL_DOCUMENTS_PAGINATION: DocumentsPaginationState = {
  limit: DOCUMENTS_PAGE_SIZE,
  offset: 0,
  total: null,
  hasMore: false,
  loading: false,
};

const App = () => {
  // Remove mock data - users and teams will be loaded from database
  const [users, setUsers] = useState<User[]>([]);
  const [teams, setTeams] = useState<Team[]>([]);

  // Initialize without a default demo user to avoid flashing unscoped data
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  // Track initial auth bootstrap to prevent landing-page flash
  const [authBooting, setAuthBooting] = useState<boolean>(true);
  // Track data loading states
  const [dataLoading, setDataLoading] = useState<boolean>(false);
  const [dataError, setDataError] = useState<string | null>(null);
  const [documentsPagination, setDocumentsPagination] = useState<DocumentsPaginationState>({ ...INITIAL_DOCUMENTS_PAGINATION });
  const [pricingPlans, setPricingPlans] = useState<PricingPlan[]>([]);
  const [testimonials, setTestimonials] = useState<Testimonial[]>([]);
  const [publicTemplates, setPublicTemplates] = useState<Template[]>([]);
  // Start on home; auth boot effect will switch to dashboard when ready
  const [view, setView] = useState<View>('home');
  // Navigate Dashboard after actions (e.g., after saving doc)
  const [dashboardNavigate, setDashboardNavigate] = useState<{ view: DashboardView; selectedFolderId?: string | null; anchorDocId?: string } | null>(null);
  const [authError, setAuthError] = useState<string | null>(null);

  // This state simulates which document is being signed, based on URL token
  const [signingState, setSigningState] = useState<{ document: DocType, token: string } | null>(null);
  const [verificationStatus, setVerificationStatus] = useState<'verifying' | 'success' | 'failed'>('verifying');
  const [passwordResetToken, setPasswordResetToken] = useState<string | null>(null);

  // Simulate routing
  useEffect(() => {
    const safeSetView = (target: View) => setView(prev => (prev === target ? prev : target));
    const handleHashChange = () => {
      const hash = window.location.hash;
      if (hash.startsWith('#/verify/')) {
        const token = hash.split('/')[2];
        handleVerification(token);
        safeSetView('verification');
      } else if (hash.startsWith('#/reset-password/')) {
        const token = hash.split('/')[2];
        setPasswordResetToken(token);
        safeSetView('resetPassword');
      } else if (hash.startsWith('#/sign/')) {
        const token = hash.split('/')[2];
        handleSignatureRequest(token);
        safeSetView('signature');
      } else if (hash === '#/terms') {
        safeSetView('terms');
      } else if (hash === '#/privacy') {
        safeSetView('privacy');
      } else if (hash === '#/admin') {
        if (currentUser?.isSuperAdmin) {
          safeSetView('admin');
        } else {
          window.location.hash = '';
        }
      } else if (!currentUser) {
        // Only show home when we definitively know user is logged out
        if (!authBooting) { safeSetView('home'); }
      }
    };

    window.addEventListener('hashchange', handleHashChange);
    handleHashChange(); // Check hash on initial load

    return () => window.removeEventListener('hashchange', handleHashChange);
    // Only set up once per session change to keep logic simple

  }, [currentUser, authBooting]);




  // Map Supabase user into local User type with sensible defaults
  const planQuota = (plan: string): { quotaTotal: number | typeof Infinity } => {
    switch (plan) {
      case 'Premium':
      case 'Enterprise':
        return { quotaTotal: Infinity };
      case 'Registered User':
      default:
        return { quotaTotal: 5 };
    }
  };

  const mapSupabaseUserToLocal = (su: { id: string; email?: string | null }): User => ({
    id: su.id,
    email: su.email || '<EMAIL>',
    password: '',
    isVerified: true,
    status: 'active',
    createdAt: new Date().toISOString(),
    planExpiryDate: undefined,
    documents: [],
    folders: [],
    notifications: [],
    quotaUsed: 0,
    quotaTotal: planQuota('Registered User').quotaTotal as number,
    planName: 'Registered User',
    name: undefined,
    username: undefined,
    avatarUrl: undefined,
    teamId: null,
    theme: 'system',
    notificationPreferences: { comments: true, shares: true, signatures: true, team: true, marketing: false },
    connections: [],
    flows: [],
    flowRuns: [],
  });

  // Provide access token to API helper
  useEffect(() => {
    if (__LEXIGEN_INIT_STATIC_BOOT_DONE__) { return; }
    __LEXIGEN_INIT_STATIC_BOOT_DONE__ = true;
    (async () => {
      try {
        const [plans, cmsTestimonials] = await Promise.all([
          fetchPublicPricingPlans(),
          fetchTestimonials(),
        ]);
        setPricingPlans(plans);
        setTestimonials(cmsTestimonials);
      } catch (error) {
        // eslint-disable-next-line no-console
        console.warn('Failed to preload marketing content.', error);
      }
    })();
    setAccessTokenGetter(async () => {
      const { data } = await supabase.auth.getSession();
      return data.session?.access_token ?? null;
    });
  }, []);

  const refreshQuotaFromServer = async () => {
    try {
      const resp = await apiFetch<{ planName: string; limit: number | null; used: number; remaining: number | null; period: { start: string; end: string } }>(`/api/quota`);
      if (currentUser) {
        updateUserState(currentUser.id, u => ({
          ...u,
          quotaUsed: resp.used,
        }));
      }
    } catch {
      // ignore
    }
  };

  // Fetch public templates when authenticated
  useEffect(() => {
    if (!currentUser) { return; }
    (async () => {
      try {
        const resp = await apiFetch<{ templates: { id: string; title: string; description: string; category: string; prompt: string; required_plan: string }[] }>(`/api/templates`);
        const rows = resp.templates || [];
        const tpls: Template[] = rows.map((r) => ({
          id: r.id,
          title: r.title,
          description: r.description,
          category: r.category,
          prompt: r.prompt,
          requiredPlan: (r.required_plan || 'free') as 'Registered User' | 'Premium',
        }));
        setPublicTemplates(tpls);
      } catch {
        // Failed to load public templates - continue silently
      }
    })();
  }, [currentUser]);

  // Apply theme to the document root on preference change
  useEffect(() => {
    const applyTheme = (theme: Theme | undefined) => {
      const root = document.documentElement;
      const prefersDark = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;
      const shouldDark = theme === 'dark' || (theme === 'system' && prefersDark);
      root.classList.toggle('dark', !!shouldDark);
    };
    applyTheme(currentUser?.theme);
    const mql = window.matchMedia('(prefers-color-scheme: dark)');
    const handler = () => applyTheme(currentUser?.theme);
    mql.addEventListener?.('change', handler);
    return () => mql.removeEventListener?.('change', handler);
  }, [currentUser?.theme]);

  type DocumentsListResponse = {
    documents: { id: string; name: string; content?: string; created_at: string; updated_at: string; folder_id?: string; client_id?: string; status?: string; value?: number; tags?: string[] }[];
    pagination?: {
      limit?: number;
      offset?: number;
      nextOffset?: number;
      total?: number | null;
      hasMore?: boolean;
    };
  };

  // Load documents from API and merge into current user
  const loadDocumentsFromApi = async ({ append = false }: { append?: boolean } = {}) => {
    const limit = documentsPagination.limit;
    const offset = append ? documentsPagination.offset : 0;

    setDocumentsPagination(prev => ({
      ...prev,
      loading: true,
      ...(append ? {} : { offset: 0, total: null, hasMore: false }),
    }));

    try {
      const params = new URLSearchParams({ limit: String(limit), offset: String(offset) });
      const resp = await apiFetch<DocumentsListResponse>(`/api/documents?${params.toString()}`);
      const basicDocuments = resp.documents || [];
      const documentIds = basicDocuments.map(doc => doc.id);

      if (documentIds.length === 0) {
        if (!append) {
          setCurrentUser(prev => prev ? { ...prev, documents: [] } : prev);
        }
        const pagination = resp.pagination;
        setDocumentsPagination(prev => {
          const resolvedLimit = typeof pagination?.limit === 'number' ? pagination.limit : limit;
          const nextOffset = typeof pagination?.nextOffset === 'number' ? pagination.nextOffset : offset;
          const totalFromResponse = typeof pagination?.total === 'number' ? pagination.total : (append ? prev.total : null);
          const hasMoreFromResponse = pagination?.hasMore;
          const hasMore = typeof hasMoreFromResponse === 'boolean'
            ? hasMoreFromResponse
            : (totalFromResponse !== null ? nextOffset < totalFromResponse : false);
          return {
            limit: resolvedLimit,
            offset: nextOffset,
            total: totalFromResponse,
            hasMore,
            loading: false,
          };
        });
        return;
      }

      // Then fetch enriched data using batch endpoint
      const batchResp = await apiFetch<{ documents: unknown[] }>(`/api/documents/batch`, {
        method: 'POST',
        body: JSON.stringify({
          documentIds,
          includeComments: true,
          includeActivityLogs: true,
          includeMetadata: true,
          includeVersions: true,
          includeCollaborators: true,
          includeSignatures: true
        })
      });

      const docs: DocType[] = (batchResp.documents || []).map((row: unknown) => {
        const r = row as Record<string, unknown>;
        return {
          id: r.id as string,
          name: r.name as string,
          content: (r.content as string) || '',
          createdAt: r.created_at as string,
          updatedAt: r.updated_at as string,
          folderId: (r.folder_id as string) || null,
          clientId: typeof r.client_id !== 'undefined' ? ((r.client_id as string) ?? null) : null,
          metadata: r.metadata ? {
            wordCount: (r.metadata as Record<string, unknown>).wordCount as number,
            lastModified: (r.metadata as Record<string, unknown>).lastModified as string,
            version: (r.metadata as Record<string, unknown>).version as number
          } : undefined,
          // Transform versions from snake_case to camelCase
          versions: ((r.versions as unknown[]) || []).map((v: unknown) => {
            const version = v as Record<string, unknown>;
            return {
              versionId: version.id as string,
              content: version.content as string,
              savedAt: version.saved_at as string,
              version: version.version as number
            };
          }),
          // Transform collaborators from snake_case to camelCase
          collaborators: ((r.collaborators as unknown[]) || []).map((c: unknown) => {
            const collab = c as Record<string, unknown>;
            return {
              email: collab.email as string,
              permission: collab.permission as Permission,
              avatarUrl: collab.avatar_url as string,
            };
          }),
          // Transform comment threads from snake_case to camelCase
          commentThreads: ((r.comments as unknown[]) || []).map((thread: unknown) => {
            const t = thread as Record<string, unknown>;
            return {
              id: t.id as string,
              textSelection: t.text_selection as string,
              isResolved: t.is_resolved as boolean,
              comments: ((t.comments as unknown[]) || []).map((comment: unknown) => {
                const c = comment as Record<string, unknown>;
                return {
                  id: c.id as string,
                  authorEmail: c.author_email as string,
                  content: c.content as string,
                  createdAt: c.created_at as string,
                };
              }),
            };
          }),
          status: (r.status as DocumentStatus) || 'draft',
          // Transform signatures from snake_case to camelCase
          signatures: ((r.signatures as unknown[]) || []).map((s: unknown) => {
            const sig = s as Record<string, unknown>;
            return {
              id: sig.id as string,
              email: sig.email as string,
              role: sig.role as string,
              status: sig.status as 'pending' | 'signed',
              signedAt: sig.signed_at as string,
              token: sig.token as string,
            };
          }),
          // Transform activity logs from snake_case to camelCase
          activityLogs: ((r.activityLogs as unknown[]) || []).map((log: unknown) => {
            const l = log as Record<string, unknown>;
            return {
              id: l.id as string,
              type: l.type as 'create' | 'edit' | 'share' | 'comment' | 'view' | 'signature' | 'revert' | 'team' | 'approval',
              userEmail: l.user_email as string,
              details: l.details as string,
              timestamp: l.timestamp as string,
            };
          }),
          value: typeof r.value === 'number' ? r.value : undefined,
          tags: Array.isArray(r.tags) ? (r.tags as string[]) : [],
        };
      });

      setCurrentUser(prev => {
        if (!prev) { return prev; }
        const nextDocuments = append ? [...prev.documents, ...docs] : docs;
        return { ...prev, documents: nextDocuments };
      });

      const pagination = resp.pagination;
      setDocumentsPagination(prev => {
        const resolvedLimit = typeof pagination?.limit === 'number' ? pagination.limit : limit;
        const nextOffset = typeof pagination?.nextOffset === 'number' ? pagination.nextOffset : offset + docs.length;
        const totalFromResponse = typeof pagination?.total === 'number' ? pagination.total : prev.total;
        const hasMoreFromResponse = pagination?.hasMore;
        const hasMore = typeof hasMoreFromResponse === 'boolean'
          ? hasMoreFromResponse
          : (totalFromResponse !== null ? nextOffset < totalFromResponse : docs.length === resolvedLimit);
        return {
          limit: resolvedLimit,
          offset: nextOffset,
          total: totalFromResponse,
          hasMore,
          loading: false,
        };
      });
    } catch (error) {
      setDocumentsPagination(prev => ({ ...prev, loading: false }));
      throw error;
    }
  };
  const loadClientsFromApi = async () => {
    try {
      const resp = await apiFetch<{ clients: { id: string; name: string; type: string; contact_person?: string; email?: string; phone?: string; address?: string; created_at: string }[] }>(`/api/clients`);
      const clients = (resp.clients || []).map((row) => ({
        id: row.id,
        name: row.name,
        type: row.type as 'Company' | 'Individual',
        contactPerson: row.contact_person || undefined,
        email: row.email || undefined,
        phone: row.phone || undefined,
        address: row.address || undefined,
        createdAt: row.created_at,
      }));
      setCurrentUser(prev => prev ? { ...prev, clients } : prev);
    } catch {
      // Failed to load clients from API
    }
  };
  const loadFoldersFromApi = async () => {
    try {
      const resp = await apiFetch<{ folders: { id: string; name: string }[] }>(`/api/folders`);
      const folders = (resp.folders || []).map((row) => ({ id: row.id, name: row.name }));
      setCurrentUser(prev => prev ? { ...prev, folders } : prev);
    } catch { /* Failed to load folders */ }
  };
  const loadClausesFromApi = async () => {
    try {
      const resp = await apiFetch<{ clauses: { id: string; title: string; content: string; tags?: string[]; created_at: string }[] }>(`/api/clauses`);
      const clauses = (resp.clauses || []).map((row) => ({
        id: row.id,
        title: row.title,
        content: row.content,
        tags: row.tags || [],
        createdAt: row.created_at,
      }));
      setCurrentUser(prev => prev ? { ...prev, clauses } : prev);
    } catch { /* Failed to load clauses */ }
  };
  const loadNotificationsFromApi = async () => {
    try {
      const resp = await apiFetch<{ notifications: { id: string; type: string; message: string; document_id?: string; created_at: string; is_read?: boolean }[] }>(`/api/notifications`);
      const notifications = (resp.notifications || []).map((n) => ({
        id: n.id,
        type: n.type as 'comment' | 'share' | 'signature' | 'team' | 'marketing' | 'approval',
        message: n.message,
        documentId: n.document_id || undefined,
        createdAt: n.created_at,
        isRead: !!n.is_read,
      }));
      setCurrentUser(prev => prev ? { ...prev, notifications } : prev);
    } catch { /* Failed to load notifications */ }
  };
  const loadTeamsFromApi = async () => {
    try {
      const resp = await apiFetch<{ teams: { id: string; name: string; owner_id: string; status?: string; created_at?: string }[] }>(`/api/teams`);
      const teams = (resp.teams || []).map((row) => ({
        id: row.id,
        name: row.name,
        ownerId: row.owner_id,
        members: [], // Members will be loaded separately if needed
        apiKeys: [],
        status: (row.status as 'active' | 'suspended') || 'active',
        workflows: [],
      }));
      setTeams(teams);
    } catch { /* Failed to load teams */ }
  };

  // Load flows from API and merge into current user
  const loadFlowsFromApi = async () => {
    try {
      const resp = await apiFetch<{ flows: { id: string; name: string; user_id: string; trigger_connection_id: string; trigger_key: string; action_connection_id: string; action_key: string; field_mapping: Record<string, string>; status: string; created_at: string }[] }>(`/api/flows`);
      const flows: Flow[] = (resp.flows || []).map((row) => ({
        id: row.id,
        name: row.name,
        userId: row.user_id,
        trigger: {
          connectionId: row.trigger_connection_id,
          triggerKey: row.trigger_key,
        },
        action: {
          connectionId: row.action_connection_id,
          actionKey: row.action_key,
        },
        fieldMapping: row.field_mapping || {},
        status: (row.status as 'active' | 'inactive') || 'inactive',
        createdAt: row.created_at,
      }));
      setCurrentUser(prev => prev ? { ...prev, flows } : prev);
    } catch { /* Failed to load flows */ }
  };

  // Load flow runs from API and merge into current user
  const loadFlowRunsFromApi = async () => {
    try {
      const resp = await apiFetch<{ runs: { id: string; flow_id: string; user_id: string; status: string; started_at: string; finished_at?: string; trigger_data?: Record<string, unknown>; action_data?: Record<string, unknown>; error?: string }[] }>(`/api/flow-runs`);
      const flowRuns: FlowRun[] = (resp.runs || []).map((row) => ({
        id: row.id,
        flowId: row.flow_id,
        userId: row.user_id,
        status: (row.status as 'success' | 'failed' | 'running') || 'running',
        startedAt: row.started_at,
        finishedAt: row.finished_at,
        triggerData: row.trigger_data,
        actionData: row.action_data,
        error: row.error,
      }));
      setCurrentUser(prev => prev ? { ...prev, flowRuns } : prev);
    } catch { /* Failed to load flow runs */ }
  };

  // Auth session boot + listener
  // Note: Admin redirect logic is centralized here to ensure is_admin users land on the admin portal.
  useEffect(() => {
    if (__LEXIGEN_INIT_AUTH_BOOT_DONE__) { return; }
    __LEXIGEN_INIT_AUTH_BOOT_DONE__ = true;
    let unsub: (() => void) | undefined;
    (async () => {
      const { data } = await supabase.auth.getSession();
      if (data.session?.user) {
        const base = mapSupabaseUserToLocal(data.session.user);
        try {
          const resp = await apiFetch<{ profile: { name?: string; username?: string; avatar_url?: string; theme?: string; plan_name?: string; quota_used?: number; notification_prefs?: Record<string, boolean>; is_admin?: boolean } }>(`/api/profile/me`);
          const p = resp.profile;
          if (p) {
            const planName: string = p.plan_name || base.planName;
            const quotaUsed = typeof p.quota_used === 'number' ? p.quota_used : base.quotaUsed;
            const quotaFromPlan = planQuota(planName).quotaTotal;
            const enriched = {
              ...base,
              name: p.name || base.name,
              username: p.username || base.username,
              avatarUrl: p.avatar_url || base.avatarUrl,
              theme: (p.theme as Theme) || base.theme,
              planName,
              quotaUsed,
              quotaTotal: quotaFromPlan as number,
              notificationPreferences: p.notification_prefs || base.notificationPreferences,
              isSuperAdmin: !!p.is_admin,
            } as User;
            setCurrentUser(enriched);
            if (p.is_admin) {
              setView('admin');
              if (window.location.hash !== '#/admin') { window.location.hash = '#/admin'; }
            } else {
              setView('dashboard');
              if (window.location.hash === '#/admin') { window.location.hash = ''; }
            }
          } else {
            setCurrentUser(base);
            setView('dashboard');
            if (window.location.hash === '#/admin') { window.location.hash = ''; }
          }
        } catch {
          setCurrentUser(base);
          setView('dashboard');
          if (window.location.hash === '#/admin') { window.location.hash = ''; }
        }
        // Reveal the app immediately after establishing session & role
        sessionStorage.setItem('lexigen_session_active', 'true');
        setAuthBooting(false);
        await refreshQuotaFromServer();

        // Load user data with proper error handling
        setDocumentsPagination({ ...INITIAL_DOCUMENTS_PAGINATION });
        setDataLoading(true);
        setDataError(null);
        try {
          await Promise.all([
            loadDocumentsFromApi(),
            loadClientsFromApi(),
            loadClausesFromApi(),
            loadNotificationsFromApi(),
            loadFoldersFromApi(),
            loadTeamsFromApi(),
            loadFlowsFromApi(),
            loadFlowRunsFromApi(),
          ]);
        } catch {
          setDataError('Failed to load some data. Please refresh the page.');
        } finally {
          setDataLoading(false);
        }
        // Avoid re-running the same hydration when the auth listener fires the initial SIGNED_IN
        __LEXIGEN_SKIP_NEXT_SIGNED_IN__ = true;
      }
      const { data: listener } = supabase.auth.onAuthStateChange(async (event, session) => {
        // Avoid duplicate initial boot and ignore token refreshes
        if (event !== 'SIGNED_IN' && event !== 'SIGNED_OUT') { return; }
        if (event === 'SIGNED_IN' && __LEXIGEN_SKIP_NEXT_SIGNED_IN__) {
          __LEXIGEN_SKIP_NEXT_SIGNED_IN__ = false;
          return;
        }
        if (session?.user) {
          const base = mapSupabaseUserToLocal(session.user);
          try {
            const resp = await apiFetch<{ profile: { name?: string; username?: string; avatar_url?: string; theme?: string; plan_name?: string; quota_used?: number; notification_prefs?: Record<string, boolean>; is_admin?: boolean } }>(`/api/profile/me`);
            const p = resp.profile;
            if (p) {
              const planName: string = p.plan_name || base.planName;
              const quotaUsed = typeof p.quota_used === 'number' ? p.quota_used : base.quotaUsed;
              const quotaFromPlan = planQuota(planName).quotaTotal;
              const enriched = {
                ...base,
                name: p.name || base.name,
                username: p.username || base.username,
                avatarUrl: p.avatar_url || base.avatarUrl,
                theme: (p.theme as Theme) || base.theme,
                planName,
                quotaUsed,
                quotaTotal: quotaFromPlan as number,
                notificationPreferences: p.notification_prefs || base.notificationPreferences,
                isSuperAdmin: !!p.is_admin,
              } as User;
              setCurrentUser(enriched);
              if (p.is_admin) {
                setView('admin');
                if (window.location.hash !== '#/admin') { window.location.hash = '#/admin'; }
              } else {
                setView('dashboard');
                if (window.location.hash === '#/admin') { window.location.hash = ''; }
              }
            } else {
              setCurrentUser(base);
              setView('dashboard');
              if (window.location.hash === '#/admin') { window.location.hash = ''; }
            }
          } catch {
            setCurrentUser(base);
            setView('dashboard');
            if (window.location.hash === '#/admin') { window.location.hash = ''; }
          }
          sessionStorage.setItem('lexigen_session_active', 'true');
          await refreshQuotaFromServer();

          // Load user data with proper error handling
          setDocumentsPagination({ ...INITIAL_DOCUMENTS_PAGINATION });
          setDataLoading(true);
          setDataError(null);
          try {
            await Promise.all([
              loadDocumentsFromApi(),
              loadClientsFromApi(),
              loadClausesFromApi(),
              loadNotificationsFromApi(),
              loadFoldersFromApi(),
              loadTeamsFromApi(),
              loadFlowsFromApi(),
              loadFlowRunsFromApi(),
            ]);
          } catch {
            setDataError('Failed to load some data. Please refresh the page.');
          } finally {
            setDataLoading(false);
          }
        } else {
          sessionStorage.removeItem('lexigen_session_active');
          setCurrentUser(null);
          setView(prev => (prev === 'home' ? prev : 'home'));
        }
      });
      unsub = () => listener.subscription.unsubscribe();
    })()
      .finally(() => {
        // Ensure boot flag is cleared if we didn't have a session
        setAuthBooting(false);
      });
    return () => { if (unsub) { unsub(); } };
  }, []);

  const handleRegister = (email: string, password: string): boolean => {
    // Kick off Supabase sign-up and rely on email verification flow
    (async () => {
      setAuthError(null);
      const { error } = await supabase.auth.signUp({
        email,
        password,
        options: { emailRedirectTo: `${window.location.origin}` },
      });
      if (error) {
        // Sign up failed
        setAuthError(error.message);
      }
    })();
    return true; // UI shows "check your email" flow
  };

  const handleLogin = (email: string, password: string) => {
    (async () => {
      setAuthError(null);
      const { error } = await supabase.auth.signInWithPassword({ email, password });
      if (error) {
        setAuthError(error.message);
        return;
      }
      // Do not set local state here; rely on onAuthStateChange 'SIGNED_IN' to hydrate once
    })();
  };

  const handleLogout = () => {
    (async () => {
      await supabase.auth.signOut();
      setCurrentUser(null);
      setDocumentsPagination({ ...INITIAL_DOCUMENTS_PAGINATION });
      setView('home');
      window.location.hash = '';
    })();
  };

  const handleVerification = (token: string) => {
    setUsers(prevUsers => {
      const userIndex = prevUsers.findIndex(u => u.verificationToken === token);
      if (userIndex !== -1) {
        const newUsers = [...prevUsers];
        newUsers[userIndex] = { ...newUsers[userIndex], isVerified: true, verificationToken: undefined };
        setVerificationStatus('success');
        return newUsers;
      } else {
        setVerificationStatus('failed');
        return prevUsers;
      }
    });
  };

  const handleForgotPassword = (email: string): boolean => {
    (async () => {
      setAuthError(null);
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}`,
      });
      if (error) {
        // Reset password failed
        setAuthError(error.message);
      }
    })();
    return true; // UI shows generic success
  };

  const handleResetPassword = (token: string, newPassword: string): boolean => {
    const userIndex = users.findIndex(u => u.passwordResetToken === token && u.resetTokenExpiry && u.resetTokenExpiry > Date.now());
    if (userIndex !== -1) {
      setUsers(prev => {
        const newUsers = [...prev];
        newUsers[userIndex] = { ...newUsers[userIndex], password: newPassword, passwordResetToken: undefined, resetTokenExpiry: undefined };
        return newUsers;
      });
      return true;
    }
    setAuthError("Invalid or expired reset token.");
    return false;
  };

  const handleLoadMoreDocuments = () => {
    if (documentsPagination.loading) { return; }
    void loadDocumentsFromApi({ append: true }).catch(() => undefined);
  };

  const handleSignatureRequest = (token: string) => {
    for (const user of users) {
      const doc = user.documents.find(d => d.signatures.some(s => s.token === token));
      if (doc) {
        setSigningState({ document: doc, token });
        return;
      }
    }
  };

  const handleSignDocument = async (token: string, _signerName: string): Promise<boolean> => {
    // This is an async stub. In a real app, this would be a network request.
    return new Promise(resolve => {
      setTimeout(() => {
        let docFound = false;
        const newUsers = users.map(user => {
          const newDocs = user.documents.map(doc => {
            const sigIndex = doc.signatures.findIndex(s => s.token === token);
            if (sigIndex !== -1) {
              docFound = true;
              const newSignatures = [...doc.signatures];
              newSignatures[sigIndex] = { ...newSignatures[sigIndex], status: 'signed', signedAt: new Date().toISOString() };
              return { ...doc, signatures: newSignatures };
            }
            return doc;
          });
          return { ...user, documents: newDocs };
        });

        if (docFound) {
          setUsers(newUsers);
          resolve(true);
        } else {
          resolve(false);
        }
      }, 500);
    });
  };

  const updateUserState = (userId: string, updateFn: (user: User) => User) => {
    setUsers(prev => prev.map(u => u.id === userId ? updateFn(u) : u));
    if (currentUser?.id === userId) {
      setCurrentUser(prev => prev ? updateFn(prev) : null);
    }
  };

  const handleSaveDocument = (content: string, name: string, folderId: string | null, clientId: string | null, options?: { tags?: string[] }) => {
    if (!currentUser) { return; }
    (async () => {
      try {
        const resp = await apiFetch<{ document: { id: string; name: string; content?: string; created_at: string; updated_at: string; folder_id?: string; client_id?: string; status?: string; tags?: string[] } }>(`/api/documents`, {
          method: 'POST',
          body: JSON.stringify({ name, content, folderId, clientId, status: 'draft', tags: options?.tags }),
        });
        const row = resp.document;
        const newDoc: DocType = {
          id: row.id,
          name: row.name,
          content: row.content || '',
          createdAt: row.created_at,
          updatedAt: row.updated_at,
          // Ensure we preserve the selected folder if API doesn't echo it back
          folderId: (typeof row.folder_id !== 'undefined' && row.folder_id !== null) ? row.folder_id : (folderId ?? null),
          // Preserve selected client if API returns null (e.g., backend ignoring nullables)
          clientId: (typeof row.client_id !== 'undefined' && row.client_id !== null) ? row.client_id : (clientId ?? null),
          tags: Array.isArray(row.tags) ? row.tags : (options?.tags || []),
          // New documents start with empty arrays
          versions: [],
          collaborators: [],
          commentThreads: [],
          status: (row.status as DocumentStatus) || 'draft',
          signatures: [],
          activityLogs: [],
        };
        updateUserState(currentUser.id, u => ({ ...u, documents: [...u.documents, newDoc] }));
        setDocumentsPagination(prev => ({
          ...prev,
          offset: prev.offset + 1,
          total: typeof prev.total === 'number' ? prev.total + 1 : prev.total,
        }));
        // Navigate to Documents -> selected folder for immediate visibility, with anchor
        setDashboardNavigate({ view: 'history', selectedFolderId: folderId ?? null, anchorDocId: newDoc.id });
        // Refresh quota from server for accurate month-to-date counts
        await apiFetch<{ used?: number }>(`/api/quota`).then((q) => {
          updateUserState(currentUser.id, u => ({ ...u, quotaUsed: typeof q.used === 'number' ? q.used : u.quotaUsed }));
        }).catch(() => { });
      } catch (e: unknown) {
        const msg = e instanceof Error ? e.message : String(e);
        if (msg.toLowerCase().includes('monthly quota')) {
          alert('Monthly quota reached for your current plan. Please upgrade to continue creating new documents.');
          return;
        }
        // Falling back to local doc create due to API error
        const newDoc: DocType = {
          id: `doc_${Date.now()}`,
          name,
          content,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          folderId,
          clientId,
          tags: options?.tags || [],
          versions: [],
          collaborators: [],
          commentThreads: [],
          status: 'draft',
          signatures: [],
          activityLogs: [],
        };
        updateUserState(currentUser.id, u => ({ ...u, documents: [...u.documents, newDoc] }));
        setDocumentsPagination(prev => ({
          ...prev,
          offset: prev.offset + 1,
          total: typeof prev.total === 'number' ? prev.total + 1 : prev.total,
        }));
        // Navigate even on fallback create
        setDashboardNavigate({ view: 'history', selectedFolderId: folderId ?? null, anchorDocId: newDoc.id });
      }
    })();
  };

  const handleUpdateDocument = (docId: string, content: string) => {
    if (!currentUser) { return; }
    (async () => {
      try {
        await apiFetch<{ document: { id: string; content: string; updated_at: string } }>(`/api/documents/${docId}`, {
          method: 'PUT',
          body: JSON.stringify({ content }),
        });
        updateUserState(currentUser.id, u => ({
          ...u,
          documents: u.documents.map(d => d.id === docId ? {
            ...d,
            content,
            updatedAt: new Date().toISOString(),
            versions: [...d.versions, { versionId: `v_${Date.now()}`, content, savedAt: new Date().toISOString(), version: (d.metadata?.version ?? d.versions.length + 1) }],
            metadata: d.metadata ? { ...d.metadata, version: d.metadata.version + 1 } : undefined
          } : d)
        }));
      } catch {
        // Falling back to local doc update due to API error
        updateUserState(currentUser.id, u => ({
          ...u,
          documents: u.documents.map(d => d.id === docId ? {
            ...d,
            content,
            updatedAt: new Date().toISOString(),
            versions: [...d.versions, { versionId: `v_${Date.now()}`, content, savedAt: new Date().toISOString(), version: (d.metadata?.version ?? d.versions.length + 1) }],
            metadata: d.metadata ? { ...d.metadata, version: d.metadata.version + 1 } : undefined
          } : d)
        }));
      }
    })();
  };

  const handleDeleteDocument = (docId: string) => {
    if (!currentUser) { return; }
    (async () => {
      try {
        await apiFetch<void>(`/api/documents/${docId}`, { method: 'DELETE' });
      } catch {
        // API delete failed, removing locally
      }
      updateUserState(currentUser.id, u => ({ ...u, documents: u.documents.filter(d => d.id !== docId) }));
      setDocumentsPagination(prev => {
        const nextOffset = Math.max(0, prev.offset - 1);
        const nextTotal = typeof prev.total === 'number' && prev.total > 0 ? prev.total - 1 : prev.total;
        const hasMore = typeof nextTotal === 'number' ? nextOffset < nextTotal : prev.hasMore;
        return {
          ...prev,
          offset: nextOffset,
          total: nextTotal,
          hasMore,
        };
      });
    })();
  };

  const ensurePersonalTeam = (): string | null => {
    if (!currentUser) { return null; }
    if (currentUser.teamId) { return currentUser.teamId; }
    const newTeamId = `team_${Date.now()}`;
    const personalTeam: Team = {
      id: newTeamId,
      name: `${currentUser.name || 'Personal'} Workspace`,
      ownerId: currentUser.id,
      members: [{ userId: currentUser.id, email: currentUser.email, role: 'Admin', avatarUrl: currentUser.avatarUrl }],
      status: 'active',
      workflows: [],
    };
    setTeams(prev => [...prev, personalTeam]);
    // attach teamId to current user
    setCurrentUser(prev => prev ? { ...prev, teamId: newTeamId } : prev);
    updateUserState(currentUser.id, u => ({ ...u, teamId: newTeamId }));
    return newTeamId;
  };

  const onCreateWorkflowTemplate = (templateData: Omit<WorkflowTemplate, 'id' | 'status'>) => {
    if (!currentUser) { return; }
    const teamId = currentUser.teamId || ensurePersonalTeam();
    if (!teamId) { return; }
    const newTemplate: WorkflowTemplate = {
      ...templateData,
      id: `wft_${Date.now()}`,
      status: 'inactive'
    };
    setTeams(prevTeams => prevTeams.map(t => {
      if (t.id === teamId) {
        return { ...t, workflows: [...(t.workflows || []), newTemplate] };
      }
      return t;
    }));
  };

  const onUpdateWorkflowTemplate = (template: WorkflowTemplate) => {
    if (!currentUser) { return; }
    const teamId = currentUser.teamId || ensurePersonalTeam();
    if (!teamId) { return; }
    setTeams(prev => prev.map(t => {
      if (t.id === teamId) {
        return { ...t, workflows: (t.workflows || []).map(w => w.id === template.id ? template : w) };
      }
      return t;
    }));
  };

  const onDeleteWorkflowTemplate = (templateId: string) => {
    if (!currentUser) { return; }
    const teamId = currentUser.teamId || ensurePersonalTeam();
    if (!teamId) { return; }
    setTeams(prevTeams => prevTeams.map(t => {
      if (t.id === teamId) {
        return { ...t, workflows: (t.workflows || []).filter(wf => wf.id !== templateId) };
      }
      return t;
    }));
  };

  const onCreateConnection = (connectorId: string, credentials: Record<string, unknown>) => {
    if (!currentUser) { return; }
    const newConnection: UserConnection = {
      id: `conn_${Date.now()}`,
      connectorId,
      userId: currentUser.id,
      credentials,
      createdAt: new Date().toISOString(),
    };
    updateUserState(currentUser.id, u => ({ ...u, connections: [...(u.connections || []), newConnection] }));
  };

  const onDeleteConnection = (connectionId: string) => {
    if (!currentUser) { return; }
    updateUserState(currentUser.id, u => ({ ...u, connections: (u.connections || []).filter(c => c.id !== connectionId) }));
  };

  const onCreateFlow = async (flowData: Omit<Flow, 'id' | 'userId' | 'createdAt'>) => {
    if (!currentUser) { return; }
    try {
      const resp = await apiFetch<{ flow: { id: string; name: string; user_id: string; trigger_connection_id: string; trigger_key: string; action_connection_id: string; action_key: string; field_mapping: Record<string, string>; status: string; created_at: string } }>(`/api/flows`, {
        method: 'POST', body: JSON.stringify({
          name: flowData.name,
          trigger: flowData.trigger,
          action: flowData.action,
          fieldMapping: flowData.fieldMapping,
          status: flowData.status,
        })
      });
      const row = resp.flow;
      const newFlow: Flow = {
        id: row.id,
        name: row.name,
        userId: row.user_id,
        trigger: { connectionId: row.trigger_connection_id, triggerKey: row.trigger_key },
        action: { connectionId: row.action_connection_id, actionKey: row.action_key },
        fieldMapping: row.field_mapping || {},
        status: (row.status as 'active' | 'inactive') || 'inactive',
        createdAt: row.created_at,
      };
      updateUserState(currentUser.id, u => ({ ...u, flows: [...(u.flows || []), newFlow] }));
    } catch { /* Create flow failed */ }
  };

  const onUpdateFlow = async (flowId: string, updates: Partial<Flow>) => {
    if (!currentUser) { return; }
    try {
      const body = {
        name: updates.name,
        status: updates.status,
        fieldMapping: updates.fieldMapping,
      };
      if ('trigger' in updates && updates.trigger && 'trigger' in body) { body.trigger = updates.trigger; }
      if ('action' in updates && updates.action && 'action' in body) { body.action = updates.action; }
      const resp = await apiFetch<{ flow: { id: string; name: string; user_id: string; trigger_connection_id: string; trigger_key: string; action_connection_id: string; action_key: string; field_mapping: Record<string, string>; status: string; created_at: string } }>(`/api/flows/${flowId}`, { method: 'PUT', body: JSON.stringify(body) });
      const row = resp.flow;
      const updated: Flow = {
        id: row.id,
        name: row.name,
        userId: row.user_id,
        trigger: { connectionId: row.trigger_connection_id, triggerKey: row.trigger_key },
        action: { connectionId: row.action_connection_id, actionKey: row.action_key },
        fieldMapping: row.field_mapping || {},
        status: (row.status as 'active' | 'inactive') || 'inactive',
        createdAt: row.created_at,
      };
      updateUserState(currentUser.id, u => ({
        ...u,
        flows: (u.flows || []).map(f => f.id === flowId ? updated : f)
      }));
    } catch { /* Update flow failed */ }
  };

  const onDeleteFlow = async (flowId: string) => {
    if (!currentUser) { return; }
    try { await apiFetch<void>(`/api/flows/${flowId}`, { method: 'DELETE' }); } catch { /* Delete flow failed */ }
    updateUserState(currentUser.id, u => ({
      ...u,
      flows: (u.flows || []).filter(f => f.id !== flowId)
    }));
  };

  // Admin: pricing plan management
  const refreshPricingPlans = async () => {
    try {
      const plans = await fetchPublicPricingPlans();
      setPricingPlans(plans);
    } catch { /* Failed to refresh pricing plans */ }
  };

  const onCreatePlan = async (planData: Omit<PricingPlan, 'id'>) => {
    try {
      await apiFetch<{ plan: { id: string; name: string; price: string } }>(`/api/pricing-plans`, {
        method: 'POST',
        body: JSON.stringify({
          name: planData.name,
          price: planData.price,
          priceDetail: planData.priceDetail,
          features: planData.features,
          cta: planData.cta,
          isFeatured: planData.isFeatured,
          // place new plan at end by default
          sortOrder: (pricingPlans?.length || 0) + 1,
        }),
      });
      await refreshPricingPlans();
    } catch { /* Create plan failed */ }
  };

  const onUpdatePlan = async (plan: PricingPlan) => {
    try {
      await apiFetch<{ plan: { id: string; name: string; price: string } }>(`/api/pricing-plans/${plan.id}`, {
        method: 'PUT',
        body: JSON.stringify({
          name: plan.name,
          price: plan.price,
          priceDetail: plan.priceDetail,
          features: plan.features,
          cta: plan.cta,
          isFeatured: plan.isFeatured,
          sortOrder: plan.sortOrder,
        }),
      });
      await refreshPricingPlans();
    } catch { /* Update plan failed */ }
  };

  const onDeletePlan = async (planId: string) => {
    try {
      await apiFetch<void>(`/api/pricing-plans/${planId}`, { method: 'DELETE' });
      await refreshPricingPlans();
    } catch { /* Delete plan failed */ }
  };

  const onUpdateSubscription = async (_teamId: string, planName: string) => {
    if (!currentUser) { return; }
    // Optimistic update for immediate UI feedback
    const optimisticQuota = planQuota(planName).quotaTotal;
    updateUserState(currentUser.id, u => ({ ...u, planName, quotaTotal: optimisticQuota as number }));
    try {
      await apiFetch<{ profile: { plan_name?: string } }>(`/api/profile/me`, { method: 'PUT', body: JSON.stringify({ planName }) });
      // Read back the profile to ensure we reflect exactly what's stored
      const resp = await apiFetch<{ profile: { plan_name?: string } }>(`/api/profile/me`);
      const p = resp.profile;
      if (p) {
        const confirmedPlan: string = p.plan_name || planName;
        const confirmedQuota = planQuota(confirmedPlan).quotaTotal;
        updateUserState(currentUser.id, u => ({ ...u, planName: confirmedPlan, quotaTotal: confirmedQuota as number }));
        setCurrentUser(prev => prev ? ({ ...prev, planName: confirmedPlan, quotaTotal: confirmedQuota as number }) : prev);
      } else {
        // Fallback: reflect requested plan
        setCurrentUser(prev => prev ? ({ ...prev, planName, quotaTotal: optimisticQuota as number }) : prev);
      }
    } catch {
      // If server fails, revert plan change
      // Failed to update subscription - reverting plan change
    }
    // Refresh server-backed month-to-date usage and UI
    await refreshQuotaFromServer();
  };

  // Splash overlay fade control - only show on initial load, not on page refresh
  const [showSplash, setShowSplash] = useState<boolean>(() => {
    // Check if this is a page refresh by looking for existing session data
    const hasExistingSession = sessionStorage.getItem('lexigen_session_active') === 'true';
    return !hasExistingSession;
  });
  const [splashFading, setSplashFading] = useState<boolean>(false);

  useEffect(() => {
    if (!authBooting && showSplash && !splashFading) {
      setSplashFading(true);
      const t = setTimeout(() => setShowSplash(false), 450);
      return () => clearTimeout(t);
    }
  }, [authBooting, showSplash, splashFading]);

  if (!currentUser) {
    switch (view) {
      case 'auth': return <><Header setView={setView} currentUser={null} onLogout={() => { }} /><AuthPage setView={setView} handleRegister={handleRegister} handleLogin={handleLogin} handleForgotPassword={handleForgotPassword} authError={authError} setAuthError={setAuthError} /></>;
      case 'verification': return <VerificationPage status={verificationStatus} setView={setView} />;
      case 'resetPassword': return <ResetPasswordPage token={passwordResetToken} handleResetPassword={handleResetPassword} setView={setView} authError={authError} setAuthError={setAuthError} />;
      case 'signature': return <SignaturePage document={signingState?.document || null} token={signingState?.token || ''} onSign={handleSignDocument} />;
      case 'terms': return <TermsPage content={INITIAL_TERMS_CONTENT} />;
      case 'privacy': return <PrivacyPage content={INITIAL_PRIVACY_CONTENT} />;
      case 'home':
      default: return <>
        <Header setView={setView} currentUser={null} onLogout={() => { }} />
        <LandingPage setView={setView} pricingPlans={pricingPlans} testimonials={testimonials} />
        <CookieConsent />
        {showSplash && (
          <div className={`fixed inset-0 z-50 transition-opacity duration-500 ${splashFading ? 'opacity-0 pointer-events-none' : 'opacity-100'}`}>
            <SplashScreen message={authBooting ? 'Signing you in' : undefined} />
          </div>
        )}
      </>;
    }
  }

  if (view === 'admin') {
    return <>
      <AdminLayout
        user={currentUser}
        onLogout={handleLogout}
        allUsers={users}
        allTeams={teams}
        onUpdateSubscription={onUpdateSubscription}
        onToggleUserStatus={() => { }}
        onManualVerifyUser={() => { }}
        onTriggerPasswordReset={() => { }}
        pricingPlans={pricingPlans}
        onCreatePlan={onCreatePlan}
        onUpdatePlan={onUpdatePlan}
        onDeletePlan={onDeletePlan}
        publicTemplates={publicTemplates}
        onCreatePublicTemplate={() => { }}
        onUpdatePublicTemplate={() => { }}
        onDeletePublicTemplate={() => { }}
        termsContent={INITIAL_TERMS_CONTENT}
        privacyContent={INITIAL_PRIVACY_CONTENT}
        onUpdateStaticContent={() => { }}
        onUpdateProfile={async (profile) => {
          if (!currentUser) { return; }
          try {
            await apiFetch<{ profile: { name?: string; username?: string; avatar_url?: string } }>(`/api/profile/me`, {
              method: 'PUT',
              body: JSON.stringify({
                name: profile.name,
                username: profile.username,
                avatarUrl: profile.avatarUrl,
              })
            });
          } catch { /* Failed to update profile on server */ }
          updateUserState(currentUser.id, u => ({
            ...u,
            name: profile.name ?? u.name,
            username: profile.username ?? u.username,
            avatarUrl: profile.avatarUrl ?? u.avatarUrl,
          }));
        }}
        onChangePassword={() => null}
        onUpdateUserSettings={async (settings) => {
          if (!currentUser) { return; }
          updateUserState(currentUser.id, u => ({
            ...u,
            theme: settings.theme ?? u.theme,
          }));
          try {
            await apiFetch<{ profile: { theme?: string } }>(`/api/profile/me`, { method: 'PUT', body: JSON.stringify({ theme: settings.theme }) });
          } catch { /* Failed to update user settings on server */ }
        }}
        onCancelSubscription={() => { /* onCancelSubscription */ }}
        stripeConfig={{ isLiveMode: false, livePublishableKey: '', liveSecretKey: '', testPublishableKey: '', testSecretKey: '', webhookSecret: '' }}
        onUpdateStripeConfig={() => { /* onUpdateStripeConfig */ }}
      />
      {showSplash && (
        <div className={`fixed inset-0 z-50 transition-opacity duration-500 ${splashFading ? 'opacity-0 pointer-events-none' : 'opacity-100'}`}>
          <SplashScreen message={authBooting ? 'Signing you in' : undefined} />
        </div>
      )}
    </>;
  }

  const currentTeam = teams.find(t => t.id === currentUser.teamId);

  const dashboardProps = {
    user: currentUser,
    team: currentTeam,
    allUsers: users,
    allTeams: teams,
    dataLoading,
    dataError,
    documentsPagination,
    onLogout: handleLogout,
    navigate: dashboardNavigate,
    onConsumeNavigate: () => setDashboardNavigate(null),
    onSaveDocument: handleSaveDocument,
    onUpdateDocument: handleUpdateDocument,
    onDeleteDocument: handleDeleteDocument,
    onLoadMoreDocuments: handleLoadMoreDocuments,
    onCreateFolder: async (name: string) => {
      try {
        const resp = await apiFetch<{ folder: { id: string; name: string } }>(`/api/folders`, { method: 'POST', body: JSON.stringify({ name }) });
        const row = resp.folder;
        updateUserState(currentUser.id, u => ({ ...u, folders: [...u.folders, { id: row.id, name: row.name }] }));
      } catch { /* Create folder failed */ }
    },
    onMoveDocument: async (docId: string, folderId: string | null) => {
      try { await apiFetch<{ document: { id: string; folder_id?: string } }>(`/api/documents/${docId}`, { method: 'PUT', body: JSON.stringify({ folderId }) }); } catch { /* Move document failed */ }
      updateUserState(currentUser.id, u => ({ ...u, documents: u.documents.map(d => d.id === docId ? { ...d, folderId } : d) }));
    },
    onUpdateCollaborators: (docId: string, collaborators: Collaborator[]) => updateUserState(currentUser.id, u => ({ ...u, documents: u.documents.map(d => d.id === docId ? { ...d, collaborators } : d) })),
    onUpdateDocumentStatus: (docId: string, newStatus: DocumentStatus) => updateUserState(currentUser.id, u => ({ ...u, documents: u.documents.map(d => d.id === docId ? { ...d, status: newStatus } : d) })),
    onRevertDocumentVersion: async (documentId: string, versionId: string) => {
      const response = await apiFetch<{ success: boolean; document: { id: string; content: string; updated_at: string } }>(`/api/document-versions/versions/${versionId}/restore`, {
        method: 'PUT'
      });
      if (response.success) {
        // Update the document in local state with the restored content
        updateUserState(currentUser.id, u => ({
          ...u,
          documents: u.documents.map(d =>
            d.id === documentId
              ? { ...d, content: response.document.content, updatedAt: response.document.updated_at }
              : d
          )
        }));
      }
    },
    onUpdateProfile: async (profile: Partial<Pick<User, 'name' | 'username' | 'avatarUrl' | 'jobTitle' | 'company' | 'bio' | 'websiteUrl' | 'linkedinUrl'>>) => {
      if (!currentUser) { return; }
      // Persist to backend
      try {
        await apiFetch<{ profile: { name?: string; username?: string; avatar_url?: string; job_title?: string; company?: string; bio?: string; website_url?: string; linkedin_url?: string } }>(`/api/profile/me`, {
          method: 'PUT',
          body: JSON.stringify({
            name: profile.name,
            username: profile.username,
            avatarUrl: profile.avatarUrl,
            jobTitle: profile.jobTitle,
            company: profile.company,
            bio: profile.bio,
            websiteUrl: profile.websiteUrl,
            linkedinUrl: profile.linkedinUrl,
          })
        });
      } catch { /* Failed to update profile on server */ }
      // Update local state
      updateUserState(currentUser.id, u => ({
        ...u,
        name: profile.name ?? u.name,
        username: profile.username ?? u.username,
        avatarUrl: profile.avatarUrl ?? u.avatarUrl,
        jobTitle: profile.jobTitle ?? u.jobTitle,
        company: profile.company ?? u.company,
        bio: profile.bio ?? u.bio,
        websiteUrl: profile.websiteUrl ?? u.websiteUrl,
        linkedinUrl: profile.linkedinUrl ?? u.linkedinUrl,
      }));
    },
    onDeleteAccount: () => { /* onDeleteAccount */ },
    onUpdateFolder: async (folderId: string, newName: string) => {
      try { await apiFetch<{ folder: { id: string; name: string } }>(`/api/folders/${folderId}`, { method: 'PUT', body: JSON.stringify({ name: newName }) }); } catch { /* Update folder failed */ }
      updateUserState(currentUser.id, u => ({ ...u, folders: u.folders.map(f => f.id === folderId ? { ...f, name: newName } : f) }));
    },
    onDeleteFolder: async (folderId: string) => {
      try { await apiFetch<void>(`/api/folders/${folderId}`, { method: 'DELETE' }); } catch { /* Delete folder failed */ }
      updateUserState(currentUser.id, u => ({ ...u, folders: u.folders.filter(f => f.id !== folderId) }));
    },
    onAddComment: async (documentId: string, textSelection: string): Promise<string | undefined> => {
      try {
        // Create a comment thread with the text selection
        const response = await apiFetch<{ thread: { id: string; document_id: string; text_selection: string; is_resolved: boolean; created_at: string } }>('/api/threads', {
          method: 'POST',
          body: JSON.stringify({
            documentId,
            textSelection
          })
        });

        // Update the document state to include the new comment thread
        updateUserState(currentUser.id, u => ({
          ...u,
          documents: u.documents.map(d => d.id === documentId ? {
            ...d,
            commentThreads: [...d.commentThreads, {
              id: response.thread.id,
              textSelection: response.thread.text_selection,
              isResolved: response.thread.is_resolved,
              comments: []
            }]
          } : d)
        }));

        return response.thread.id;
      } catch (error) {
        console.error('Failed to create comment thread:', error);
        return undefined;
      }
    },
    onAddReply: async (documentId: string, threadId: string, content: string) => {
      const response = await apiFetch<{ comment: { id: string; thread_id: string; author_email: string; content: string; created_at: string } }>('/api/comments', {
        method: 'POST',
        body: JSON.stringify({
          threadId,
          authorEmail: currentUser?.email || '',
          content
        })
      });

      // Note: Reply will be reflected in the UI after successful API call

      return response.comment.id;
    },
    onResolveThread: async (documentId: string, threadId: string) => {
      await apiFetch<{ thread: { id: string; is_resolved: boolean } }>(`/api/threads/${threadId}`, {
        method: 'PUT',
        body: JSON.stringify({
          isResolved: true
        })
      });

      // Note: Thread resolution will be reflected in the UI after successful API call
    },
    onDeleteComment: async (documentId: string, threadId: string, commentId: string) => {
      await apiFetch<void>(`/api/comments/${commentId}`, {
        method: 'DELETE'
      });

      // Note: Comment deletion will be reflected in the UI after successful API call
    },
    onMarkNotificationRead: async (notificationId: string) => {
      try { await apiFetch<{ notification: { id: string; is_read: boolean } }>(`/api/notifications/${notificationId}`, { method: 'PUT', body: JSON.stringify({ isRead: true }) }); } catch { /* Mark notification read failed */ }
      updateUserState(currentUser.id, u => ({ ...u, notifications: (u.notifications || []).map(n => n.id === notificationId ? { ...n, isRead: true } : n) }));
    },
    onMarkAllNotificationsRead: async () => {
      const ids = (currentUser.notifications || []).filter(n => !n.isRead).map(n => n.id);
      await Promise.all(ids.map(id => apiFetch(`/api/notifications/${id}`, { method: 'PUT', body: JSON.stringify({ isRead: true }) }).catch(() => { })));
      updateUserState(currentUser.id, u => ({ ...u, notifications: (u.notifications || []).map(n => ({ ...n, isRead: true })) }));
    },
    onCreateCustomTemplate: async (docId: string, templateName: string) => {
      const doc = currentUser.documents.find(d => d.id === docId);
      if (!doc) { return; }
      try {
        const resp = await apiFetch<{ template: { id: string; name: string; content: string; created_at: string } }>(`/api/custom-templates`, { method: 'POST', body: JSON.stringify({ name: templateName, content: doc.content }) });
        const row = resp.template;
        const newTpl = { id: row.id, name: row.name, content: row.content, createdAt: row.created_at };
        updateUserState(currentUser.id, u => ({ ...u, customTemplates: [...(u.customTemplates || []), newTpl] }));
      } catch { /* Create custom template failed */ }
    },
    onDeleteCustomTemplate: async (templateId: string) => {
      try { await apiFetch<void>(`/api/custom-templates/${templateId}`, { method: 'DELETE' }); } catch { /* Delete failed */ }
      updateUserState(currentUser.id, u => ({ ...u, customTemplates: (u.customTemplates || []).filter(t => t.id !== templateId) }));
    },
    onUpgradeSubscription: async () => { await onUpdateSubscription('', 'Premium'); },
    onUpdateSubscription: onUpdateSubscription,
    onRequestSignatures: () => { /* onRequestSignatures */ },
    onLogDocumentView: () => { /* onLogDocumentView */ },
    onCreateClause: async (data) => {
      try {
        const resp = await apiFetch<{ clause: { id: string; title: string; content: string; tags?: string[]; created_at: string } }>(`/api/clauses`, { method: 'POST', body: JSON.stringify(data) });
        const row = resp.clause;
        const newClause = { id: row.id, title: row.title, content: row.content, tags: row.tags || [], createdAt: row.created_at };
        updateUserState(currentUser.id, u => ({ ...u, clauses: [...(u.clauses || []), newClause] }));
      } catch { /* Create clause failed */ }
    },
    onUpdateClause: async (id, updates) => {
      try {
        const resp = await apiFetch<{ clause: { id: string; title: string; content: string; tags?: string[] } }>(`/api/clauses/${id}`, { method: 'PUT', body: JSON.stringify(updates) });
        const row = resp.clause;
        updateUserState(currentUser.id, u => ({ ...u, clauses: (u.clauses || []).map(c => c.id === id ? { ...c, title: row.title, content: row.content, tags: row.tags || [] } : c) }));
      } catch { /* Update clause failed */ }
    },
    onDeleteClause: async (id) => {
      try { await apiFetch<void>(`/api/clauses/${id}`, { method: 'DELETE' }); } catch { /* Delete failed */ }
      updateUserState(currentUser.id, u => ({ ...u, clauses: (u.clauses || []).filter(c => c.id !== id) }));
    },
    onCreateClient: async (data) => {
      try {
        const resp = await apiFetch<{ client: { id: string; name: string; type: string; contact_person?: string; email?: string; phone?: string; address?: string; created_at: string } }>(`/api/clients`, { method: 'POST', body: JSON.stringify(data) });
        const row = resp.client;
        const newClient = { id: row.id, name: row.name, type: row.type, contactPerson: row.contact_person || undefined, email: row.email || undefined, phone: row.phone || undefined, address: row.address || undefined, createdAt: row.created_at };
        updateUserState(currentUser.id, u => ({ ...u, clients: [...(u.clients || []), newClient as Client] }));
      } catch { /* Create client failed */ }
    },
    onUpdateClient: async (id, updates) => {
      try {
        const resp = await apiFetch<{ client: { id: string; name: string; type: string; contact_person?: string; email?: string; phone?: string; address?: string } }>(`/api/clients/${id}`, { method: 'PUT', body: JSON.stringify(updates) });
        const row = resp.client;
        updateUserState(currentUser.id, u => ({
          ...u, clients: (u.clients || []).map(c => c.id === id ? {
            ...c,
            name: row.name,
            type: row.type as "Company" | "Individual",
            contactPerson: row.contact_person || undefined,
            email: row.email || undefined,
            phone: row.phone || undefined,
            address: row.address || undefined
          } : c)
        }));
      } catch { /* Update client failed */ }
    },
    onDeleteClient: async (id) => {
      try { await apiFetch<void>(`/api/clients/${id}`, { method: 'DELETE' }); } catch { /* Delete failed */ }
      // Remove client and unassign from any documents
      updateUserState(currentUser.id, u => ({
        ...u,
        clients: (u.clients || []).filter(c => c.id !== id),
        documents: u.documents.map(d => d.clientId === id ? { ...d, clientId: null } : d)
      }));
    },
    onUpdateDocumentClient: async (docId: string, clientId: string | null) => {
      try {
        const resp = await apiFetch<{ document: { id: string; client_id?: string; updated_at: string } }>(`/api/documents/${docId}`, { method: 'PUT', body: JSON.stringify({ clientId }) });
        const row = resp.document;
        updateUserState(currentUser.id, u => ({
          ...u,
          documents: u.documents.map(d => d.id === docId ? { ...d, clientId: row.client_id || clientId, updatedAt: row.updated_at || d.updatedAt } : d)
        }));
      } catch {
        updateUserState(currentUser.id, u => ({ ...u, documents: u.documents.map(d => d.id === docId ? { ...d, clientId } : d) }));
      }
    },
    onInviteMember: () => { /* onInviteMember */ return null; },
    onUpdateMemberRole: () => { /* onUpdateMemberRole */ },
    onRemoveMember: () => { /* onRemoveMember */ },
    onUpdateUserSettings: async (settings: { theme?: Theme; notificationPreferences?: Partial<NotificationPreferences> }) => {
      if (!currentUser) { return; }
      // Optimistic local update so toggles reflect immediately
      updateUserState(currentUser.id, u => ({
        ...u,
        theme: settings.theme ?? u.theme,
        notificationPreferences: settings.notificationPreferences ? {
          comments: settings.notificationPreferences.comments ?? u.notificationPreferences.comments,
          shares: settings.notificationPreferences.shares ?? u.notificationPreferences.shares,
          signatures: settings.notificationPreferences.signatures ?? u.notificationPreferences.signatures,
          team: settings.notificationPreferences.team ?? u.notificationPreferences.team,
          marketing: settings.notificationPreferences.marketing ?? u.notificationPreferences.marketing
        } : u.notificationPreferences,
      }));
      try {
        await apiFetch<{ profile: { theme?: string; notification_preferences?: Record<string, boolean> } }>(`/api/profile/me`, {
          method: 'PUT', body: JSON.stringify({
            theme: settings.theme,
            notificationPreferences: settings.notificationPreferences,
          })
        });
      } catch { /* Update failed */ }
    },
    onChangePassword: () => { /* onChangePassword */ return null; },
    onGenerateApiKey: () => { /* onGenerateApiKey */ return { id: '', name: '', key: '', createdAt: '' }; },
    onRevokeApiKey: () => { /* onRevokeApiKey */ },
    onUpdateSsoConfig: () => { /* onUpdateSsoConfig */ },
    pricingPlans,
    publicTemplates,
    onRequestApproval: () => { /* onRequestApproval */ },
    onRespondToApproval: () => { /* onRespondToApproval */ },
    onUpdateObligationStatus: () => { /* onUpdateObligationStatus */ },
    onCreateWorkflowTemplate: onCreateWorkflowTemplate,
    onUpdateWorkflowTemplate: onUpdateWorkflowTemplate,
    onDeleteWorkflowTemplate: onDeleteWorkflowTemplate,
    workflowInstances: [],
    onCreateConnection: onCreateConnection,
    onDeleteConnection: onDeleteConnection,
    onCreateFlow: onCreateFlow,
    onUpdateFlow: onUpdateFlow,
    onDeleteFlow: onDeleteFlow,
  };


  return (
    <>
      <DashboardLayout {...dashboardProps} />
      {showSplash && (
        <div className={`fixed inset-0 z-50 transition-opacity duration-500 ${splashFading ? 'opacity-0 pointer-events-none' : 'opacity-100'}`}>
          <SplashScreen message={authBooting ? 'Signing you in' : undefined} />
        </div>
      )}
    </>
  );
};

export default App;
