import type { Request, Response, NextFunction } from 'express';
import { supabaseAdmin } from '../supabaseClient';

const AUTH_BYPASS_ENVS = new Set(['development', 'test']);

function isExplicitNonProductionEnv(): boolean {
  const nodeEnv = process.env.NODE_ENV;
  return typeof nodeEnv === 'string' && AUTH_BYPASS_ENVS.has(nodeEnv);
}

export interface AuthRequest<P = Record<string, string>> extends Request<P> {
  headers: Request['headers'];
  user?: { id: string; email?: string | null };
  accessToken?: string;
  requestId?: string;
}

// Helper function to safely get accessToken from authenticated request
export function getAccessToken(req: AuthRequest): string {
  if (!req.accessToken) {
    throw new Error('Access token not found - ensure requireAuth middleware is used');
  }
  return req.accessToken;
}

export async function requireAuth(req: AuthRequest, res: Response, next: NextFunction) {
  try {
    // Dev convenience: if TEST_BYPASS_AUTH is enabled OR Supabase is not configured,
    // allow requests through as a test user so local features keep working.
    const shouldBypassAuth =
      isExplicitNonProductionEnv() &&
      (
        process.env.TEST_BYPASS_AUTH === '1' ||
        !process.env.SUPABASE_URL ||
        !process.env.SUPABASE_SERVICE_ROLE_KEY
      );

    if (shouldBypassAuth) {
      req.user = { id: 'test-user', email: '<EMAIL>' };
      req.accessToken = 'test-token';
      return next();
    }
    const auth = req.headers.authorization || '';
    const token = auth.startsWith('Bearer ') ? auth.slice('Bearer '.length) : undefined;
    if (!token) {
      return res.status(401).json({ error: 'Missing Authorization header' });
    }
    const { data, error } = await supabaseAdmin.auth.getUser(token);
    if (error || !data?.user) {
      return res.status(401).json({ error: 'Invalid or expired token' });
    }
    req.user = { id: data.user.id, email: data.user.email };
    req.accessToken = token;
    next();
  } catch (err) {
    next(err);
  }
}

// Optional-auth variant: never blocks the request, but fills req.user when possible
export async function maybeAuth(req: AuthRequest, _res: Response, next: NextFunction) {
  try {
    const shouldBypassAuth =
      isExplicitNonProductionEnv() &&
      (
        process.env.TEST_BYPASS_AUTH === '1' ||
        !process.env.SUPABASE_URL ||
        !process.env.SUPABASE_SERVICE_ROLE_KEY
      );

    if (shouldBypassAuth) {
      req.user = { id: 'test-user', email: '<EMAIL>' };
      req.accessToken = 'test-token';
      return next();
    }
    const auth = req.headers.authorization || '';
    const token = auth.startsWith('Bearer ') ? auth.slice('Bearer '.length) : undefined;
    if (!token) {return next();}
    const { data } = await supabaseAdmin.auth.getUser(token);
    if (data?.user) {
      req.user = { id: data.user.id, email: data.user.email };
      req.accessToken = token;
    }
    return next();
  } catch {
    // swallow and continue as anonymous in optional mode
    return next();
  }
}
