<div align="center">
<img width="1200" height="475" alt="GHBanner" src="https://github.com/user-attachments/assets/0aa67016-6eaf-458a-adb2-6e31a0763ed6" />
</div>

# Run and deploy your AI Studio app

This contains everything you need to run your app locally.

View your app in AI Studio: https://ai.studio/apps/drive/1A7FAJEZNafhM5emgxvegrJIwC9l_3tDY

## Run Locally

**Prerequisites:** Node.js, a Supabase project

1) Install dependencies:
   `npm install`
   (This pulls in the new `rate-limit-redis` store used by the API's Redis-backed rate limiting.)

2) Configure environment variables

- Frontend: copy `.env.local.example` to `.env.local` and set:
  - `VITE_API_BASE` (e.g. `http://localhost:3002`)
  - `VITE_SUPABASE_URL` (e.g. `https://hrzspijfqgjducjixddr.supabase.co`)
  - `VITE_SUPABASE_ANON_KEY` (obtain from project maintainers)

- Backend: copy `.env.example` to `.env` and set:
  - `SUPABASE_URL` (same as frontend)
  - `SUPABASE_ANON_KEY` (obtain from project maintainers)
  - `SUPABASE_SERVICE_ROLE_KEY` (server-only secret)
  - `PORT` (default 3001)
  - `CLIENT_ORIGIN` (default http://localhost:5173)
  - `SENTRY_DSN` (required in production to enable Sentry error reporting)
  - `SENTRY_TRACES_SAMPLE_RATE` (optional, defaults to `0.1` for backend tracing)
  - `SENTRY_RELEASE` (optional, overrides the detected Git commit for release tracking)
  - `SENTRY_ENVIRONMENT` (optional, overrides the environment name reported to Sentry)
  - `REDIS_ENABLED` (`true` to enable Redis caching, rate limiting, and the event bus broker)
  - `REDIS_URL` (optional, defaults to `redis://127.0.0.1:6379`)

> **Deployment note:** Sentry instrumentation only initializes when `NODE_ENV=production` and a `SENTRY_DSN` is present. If your
> platform exposes a commit SHA (e.g., `VERCEL_GIT_COMMIT_SHA`, `GITHUB_SHA`, `HEROKU_SLUG_COMMIT`), it will automatically be used as the release unless
> you set `SENTRY_RELEASE` explicitly.

3) Create Supabase tables and policies

- In the Supabase SQL editor, run the contents of `supabase/schema.sql`.

4) Run the app (client + server):
   `npm run dev`

The frontend runs on `http://localhost:5173` and the API on `http://localhost:3001`.

### Database: Migrations & Seeding (Supabase CLI)

To push migrations and apply the seed data to your remote Supabase project:

1) Authenticate the Supabase CLI (one-time):
   `npx supabase login` (paste your personal access token)

2) Link the project (one-time):
   `npm run db:link`  (or `npx supabase link --project-ref hrzspijfqgjducjixddr`)

3) Apply migrations + seed to remote:
   `npm run db:seed`

One-liners using env vars (no prompts):

- macOS/Linux:
  `SUPABASE_ACCESS_TOKEN="<token>" SUPABASE_DB_PASSWORD="<db_password>" npm run db:seed`

- Windows PowerShell:
  `$env:SUPABASE_ACCESS_TOKEN='<token>'; $env:SUPABASE_DB_PASSWORD='<db_password>'; npm run db:seed`

- Windows CMD:
  `set SUPABASE_ACCESS_TOKEN=<token> && set SUPABASE_DB_PASSWORD=<db_password> && npm run db:seed`

Notes:
- The seed file is `supabase/seed.sql` and is idempotent (uses `WHERE NOT EXISTS`).
- Script reads `SUPABASE_ACCESS_TOKEN` and `SUPABASE_DB_PASSWORD` from your environment.
- To persist for future shells, add them to your shell profile (e.g., `~/.bashrc`, PowerShell profile) — do not commit them.

### API Summary

- `GET /health` — server health check
- `GET /api/documents?limit=20&offset=0` — list authenticated user documents (pagination supports `limit` up to 50 and `offset`)
- `POST /api/documents` — create a document `{ name, content?, folderId?, status? }`
- `PUT /api/documents/:id` — update fields
- `DELETE /api/documents/:id` — delete
- `GET /api/clauses` — list user clauses
- `POST /api/clauses` — create clause `{ title, content, tags? }`
- `PUT /api/clauses/:id` — update clause
- `DELETE /api/clauses/:id` — delete clause

Additional CRUD endpoints (all require `Authorization: Bearer <token>`, RLS enforced):

- Clients: `GET/POST /api/clients`, `PUT/DELETE /api/clients/:id`
- Folders: `GET/POST /api/folders`, `PUT/DELETE /api/folders/:id`
- Collaborators: `GET/POST /api/documents/:documentId/collaborators`, `PUT/DELETE /api/documents/collaborators/:id`
- Comment Threads: `GET/POST /api/threads`, `PUT/DELETE /api/threads/:id`
- Comments: `GET /api/threads/:threadId/comments`, `POST /api/comments`, `PUT/DELETE /api/comments/:id`
- Notifications: `GET/POST /api/notifications`, `PUT/DELETE /api/notifications/:id`
- Signatures: `GET/POST /api/signatures`, `PUT/DELETE /api/signatures/:id`
- Key Dates: `GET/POST /api/key-dates`, `PUT/DELETE /api/key-dates/:id`
- Approvers: `GET/POST /api/approvers`, `PUT/DELETE /api/approvers/:id`
- Obligations: `GET/POST /api/obligations`, `PUT/DELETE /api/obligations/:id`
- Activity Logs: `GET/POST /api/activity-logs`
- Custom Templates: `GET/POST /api/custom-templates`, `PUT/DELETE /api/custom-templates/:id`
- Public Templates: `GET /api/templates` (read-only)
- Pricing Plans: `GET /api/pricing-plans` (read-only)
- Profiles: `GET /api/profile/me`, `PUT /api/profile/me`
- Teams: `GET/POST /api/teams`, `PUT/DELETE /api/teams/:teamId`
- Team Members: `GET/POST /api/teams/:teamId/members`, `PUT/DELETE /api/teams/:teamId/members/:userId`
- Team API Keys: `GET/POST /api/teams/:teamId/api-keys`, `DELETE /api/teams/:teamId/api-keys/:id`
- Team SSO Config: `GET /api/teams/:teamId/sso-config`, `PUT /api/teams/:teamId/sso-config`
- Integrations: `GET/POST /api/integrations`, `PUT/DELETE /api/integrations/:id`
- Connections: `GET/POST /api/connections`, `PUT/DELETE /api/connections/:id`
- Flows: `GET/POST /api/flows`, `PUT/DELETE /api/flows/:id`
- Flow Runs: `GET/POST /api/flow-runs`, `PUT/DELETE /api/flow-runs/:id`
- Workflow Templates: `GET/POST /api/workflows/templates`, `PUT/DELETE /api/workflows/templates/:id`
- Workflow Nodes: `GET /api/workflows/templates/:templateId/nodes`, `POST /api/workflows/nodes`, `PUT/DELETE /api/workflows/nodes/:id`
- Workflow Edges: `GET /api/workflows/templates/:templateId/edges`, `POST /api/workflows/edges`, `PUT/DELETE /api/workflows/edges/:id`
- Workflow Instances: `GET/POST /api/workflows/instances`, `PUT/DELETE /api/workflows/instances/:id`
- Document Versions: `GET /api/documents/:documentId/versions`, `POST /api/documents/versions`, `DELETE /api/documents/versions/:id`
- Events: `GET /api/events` (SSE stream for document status updates)

All API routes require `Authorization: Bearer <supabase access token>` from Supabase Auth.

### Frontend Auth Wiring (Supabase)

Use `lib/supabaseClient.ts` to sign in and retrieve the current session token. Provide that token to the API client via `setAccessTokenGetter`:

```ts
import { supabase } from './lib/supabaseClient';
import { setAccessTokenGetter, apiFetch } from './lib/api';

setAccessTokenGetter(async () => {
  const { data } = await supabase.auth.getSession();
  return data.session?.access_token ?? null;
});

// Example usage
async function loadDocs() {
  const data = await apiFetch<{ documents: Document[] }>("/api/documents");
  console.log(data.documents);
}
```

### Marketing Content Sourcing

- The landing page loads pricing plans and testimonials through helper utilities in `lib/content.ts`.
- `fetchPublicPricingPlans` always attempts to call `/api/pricing-plans/public` first. If that API is unreachable, it falls back to the JSON snapshot stored at `public/cms/pricing-plans.json` so the UI continues to render the latest bundled pricing.
- `fetchTestimonials` reads from `public/cms/testimonials.json`, allowing the marketing team to refresh testimonials via a CMS export without changing code.
- When both the remote API and the JSON fallback are unavailable, the UI displays soft placeholder messaging in the pricing and testimonials sections instead of hard-coded demo content.
- Keep the JSON files in `public/cms/` up to date with your CMS exports to ensure the fallback content matches production data.
